const loginModel = require('../models/loginModel');

async function login(req, res) {
  try {
    const { mobile_number } = req.body;
    const response = await loginModel.login(mobile_number);
    res.status(200).json({
        status: 200,
        message: response,
        // data: response
    });
  } catch (error) {
    res.status(500).json({
        status: 500,
        message: 'OTP Sending Failed',
        error: error.message
    });
  }
}

// otp verification 
async function verifyOTP(req, res) {
    try {
        const { mobile_number, otp } = req.body;
        console.log(`OTP: ${otp}`);
        const response = await loginModel.verifyOTP(mobile_number, otp);
        // Here you would typically verify the OTP against your database or service
        // For demonstration, we'll assume OTP verification is successful
        res.status(200).json({
        status: 200,
        message: 'OTP verified successfully',
        data: response
        });
    } catch (error) {
        res.status(500).json({
        status: 500,
        message: 'OTP verification failed',
        error: error.message
        });
    }
    }
    

module.exports = {
  login,
  verifyOTP
};
