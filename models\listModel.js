const axios = require('axios');
const db = require('../config/db');

// list 
async function list() {
  try {
    const list = {
  baseURL: 'https://godrejcpteam.freshdesk.com/api/v2/custom_objects/schemas/5974899/records',
  // timeout: 10000,
  headers: {
        'Authorization': 'Basic MG9jN1UyWWc4bkM4UnMxVzhwRQ==',
        'Cookie': '_cf_bm=xOxh95NoVwozSEhMrsCcmXJX1AR2VdeZ_2V6LeVLV4I-1753763398-1.0.1.1-83xiMtxidfyynSF87kmRaKA5haRCNm4h16GK5ZG.cL51tOcjdVT1HOR810.DHFyz9pfc2g9njM9AqF._hgzT1yyY8OAkrny2juH6G4YIYA',
        'Content-Type': 'application/json',
        'Accept': 'application/json'
      }
};
    const response = await axios.get(list.baseURL, {
      headers: list.headers,
      // timeout: list.timeout
    });
    return response.data;
  } catch (error) {
    throw error;
  }
}

// userlisr

async function userlist() {
  try {
    const userlist = {
  baseURL: 'https://godrejcpteam.freshdesk.com/api/v2/search/tickets',
  // timeout: 10000,
  headers: {
        'Authorization': 'Basic MG9jN1UyWWc4bkM4UnMxVzhwRQ==',
        'Cookie': '_cf_bm=xOxh95NoVwozSEhMrsCcmXJX1AR2VdeZ_2V6LeVLV4I-1753763398-1.0.1.1-83xiMtxidfyynSF87kmRaKA5haRCNm4h16GK5ZG.cL51tOcjdVT1HOR810.DHFyz9pfc2g9njM9AqF._hgzT1yyY8OAkrny2juH6G4YIYA',
        'Content-Type': 'application/json',
        'Accept': 'application/json'
      }
    };
    const params = {
      query: '?' + '%22status%3A7%22'
    };
    const response = await axios.get(userlist.baseURL + params.query, {
      headers: userlist.headers,
      // timeout: userlist.timeout
    });
    return response.data;
  } catch (error) {
    throw error;
  }
}

module.exports = {
  list,
  userlist
};
