const axios = require('axios');
const db = require('../config/db');

// list 
async function list() {
  try {
    const list = {
  baseURL: 'https://godrejcpteam.freshdesk.com/api/v2/custom_objects/schemas/5974899/records',
  // timeout: 10000,
  headers: {
        'Authorization': 'Basic MG9jN1UyWWc4bkM4UnMxVzhwRQ==',
        'Cookie': '_cf_bm=xOxh95NoVwozSEhMrsCcmXJX1AR2VdeZ_2V6LeVLV4I-1753763398-1.0.1.1-83xiMtxidfyynSF87kmRaKA5haRCNm4h16GK5ZG.cL51tOcjdVT1HOR810.DHFyz9pfc2g9njM9AqF._hgzT1yyY8OAkrny2juH6G4YIYA',
        'Content-Type': 'application/json',
        'Accept': 'application/json'
      }
};
    const response = await axios.get(list.baseURL, {
      headers: list.headers,
      // timeout: list.timeout
    });
    return response.data;
  } catch (error) {
    throw error;
  }
}

// userlisr

async function tickets() {
  try {
    const userlist = {
  baseURL: 'https://godrejcpteam.freshdesk.com/api/v2/search/tickets?query=%22status%3A7%22',
  // timeout: 10000,
  headers: {
        'Authorization': 'Basic MG9jN1UyWWc4bkM4UnMxVzhwRQ==',
        'Cookie': '_cf_bm=xOxh95NoVwozSEhMrsCcmXJX1AR2VdeZ_2V6LeVLV4I-1753763398-1.0.1.1-83xiMtxidfyynSF87kmRaKA5haRCNm4h16GK5ZG.cL51tOcjdVT1HOR810.DHFyz9pfc2g9njM9AqF._hgzT1yyY8OAkrny2juH6G4YIYA',
        'Content-Type': 'application/json',
        'Accept': 'application/json'
      }
    };
    // const params = {
    //   query: ''
    // };
    const response = await axios.get(userlist.baseURL, {
      headers: userlist.headers,
      // timeout: userlist.timeout
      // params: params
    });
    // console.log('response', response);
    return response.data;
  } catch (error) {
    throw error;
  }
}

// custom_objects
async function custom_objects(data) {
  try {
    const custom_objects = {
  baseURL: 'https://godrejcpteam.freshdesk.com/api/v2/custom_objects/schemas/5980468/records',
  // timeout: 10000,
  headers: {
        'Authorization': 'Basic MG9jN1UyWWc4bkM4UnMxVzhwRQ==',
        'Cookie': '_cf_bm=xOxh95NoVwozSEhMrsCcmXJX1AR2VdeZ_2V6LeVLV4I-1753763398-1.0.1.1-83xiMtxidfyynSF87kmRaKA5haRCNm4h16GK5ZG.cL51tOcjdVT1HOR810.DHFyz9pfc2g9njM9AqF._hgzT1yyY8OAkrny2juH6G4YIYA',
        'Content-Type': 'application/json',
        'Accept': 'application/json'
      }
    };
    const payload = data;
    // console.log('payload', payload);
    const response = await axios.post(custom_objects.baseURL, payload, {
      headers: custom_objects.headers
    });
    console.log('response', response);
    if (response.status === 200) {
      return response.data;
    } else {
      throw new Error('Failed to create custom object');
    }
    // return response.data; 
  } catch (error) {
      throw error;
  } 
}

// submit invoice
async function submit_invoice(data) {
  try {
    const submit_invoice = {
  baseURL: 'https://godrejcpteam.freshdesk.com/api/v2/custom_objects/schemas/5980468/records/_2-3',
  // timeout: 10000,
  headers: {
        'Authorization': 'Basic MG9jN1UyWWc4bkM4UnMxVzhwRQ==',
        'Cookie': '_cf_bm=xOxh95NoVwozSEhMrsCcmXJX1AR2VdeZ_2V6LeVLV4I-1753763398-1.0.1.1-83xiMtxidfyynSF87kmRaKA5haRCNm4h16GK5ZG.cL51tOcjdVT1HOR810.DHFyz9pfc2g9njM9AqF._hgzT1yyY8OAkrny2juH6G4YIYA',
        'Content-Type': 'application/json',
        'Accept': 'application/json'
      }
    };
    const payload = data;
    const response = await axios.put(submit_invoice.baseURL, payload, {
      headers: submit_invoice.headers
    });
    if (response.status === 200) {
      return response.data;
    } else {
      throw new Error('Failed to create custom object');
    }
  } catch (error) {
    throw error;
  }
}


// for no update
async function no_update(data) {
  try {
    const no_update = {
      baseURL: 'https://godrejcpteam.freshdesk.com/api/v2/tickets/34',
      headers: {
        'Authorization': 'Basic MG9jN1UyWWc4bkM4UnMxVzhwRQ==',
        'Cookie': '_cf_bm=xOxh95NoVwozSEhMrsCcmXJX1AR2VdeZ_2V6LeVLV4I-1753763398-1.0.1.1-83xiMtxidfyynSF87kmRaKA5haRCNm4h16GK5ZG.cL51tOcjdVT1HOR810.DHFyz9pfc2g9njM9AqF._hgzT1yyY8OAkrny2juH6G4YIYA',
        'Content-Type': 'application/json',
        'Accept': 'application/json'
      }
    };
    payload = data;
    const response = await axios.put(no_update.baseURL, payload, {
      headers: no_update.headers
    });
    if (response.status === 200) {
      return response.data;
    } else {
      throw new Error('Failed to create custom object');
    }
  } catch (error) {
    throw error;
  }
}

module.exports = {
  list,
  tickets,
  custom_objects,
  submit_invoice,
  no_update
};
