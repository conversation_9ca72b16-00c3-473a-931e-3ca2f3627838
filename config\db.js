const mysql = require('mysql2');
require('dotenv').config();

// Database configuration
const dbConfig = {
  host: process.env.DB_HOST || 'localhost',
  user: process.env.DB_USER || 'root',
  password: process.env.DB_PASSWORD || '',
  database: process.env.DB_NAME || 'nodejs_app',
  port: process.env.DB_PORT || 3306,
  connectionLimit: process.env.DB_CONNECTION_LIMIT || 10,
  acquireTimeout: 60000,
  timeout: 60000,
  reconnect: true,
  charset: 'utf8mb4'
};

// Create connection pool
const pool = mysql.createPool(dbConfig);

// Create promise-based pool for async/await support
const promisePool = pool.promise();

// Test database connection
const testConnection = async () => {
  try {
    const connection = await promisePool.getConnection();
    console.log('✅ Database connected successfully');
    connection.release();
    return true;
  } catch (error) {
    console.error('❌ Database connection failed:', error.message);
    return false;
  }
};

// Handle pool events
pool.on('connection', (connection) => {
  console.log(`📊 New database connection established as id ${connection.threadId}`);
});

pool.on('error', (err) => {
  console.error('❌ Database pool error:', err);
  if (err.code === 'PROTOCOL_CONNECTION_LOST') {
    console.log('🔄 Attempting to reconnect to database...');
  }
});

// Graceful shutdown
const closePool = () => {
  return new Promise((resolve, reject) => {
    pool.end((err) => {
      if (err) {
        console.error('❌ Error closing database pool:', err);
        reject(err);
      } else {
        console.log('✅ Database pool closed successfully');
        resolve();
      }
    });
  });
};

// Export pool and utility functions
module.exports = {
  pool,
  promisePool,
  testConnection,
  closePool,
  query: (sql, params) => promisePool.execute(sql, params),
  queryOne: async (sql, params) => {
    const [rows] = await promisePool.execute(sql, params);
    return rows[0] || null;
  },
  queryAll: async (sql, params) => {
    const [rows] = await promisePool.execute(sql, params);
    return rows;
  }
};
