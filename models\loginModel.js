const axios = require('axios');
const { createClient } = require('redis');

// 🔌 Connect to Redis
const redisClient = createClient();
redisClient.connect().catch(console.error);

// 🔐 Freshdesk API config
const apiConfig = {
  baseURL: 'https://godrejcpteam.freshdesk.com/api/_/custom_objects/schemas/5974899/records/count',
  timeout: 10000,
  headers: {
        'Authorization': 'Basic MG9jN1UyWWc4bkM4UnMxVzhwRQ==',
        'Cookie': '_cf_bm=xOxh95NoVwozSEhMrsCcmXJX1AR2VdeZ_2V6LeVLV4I-1753763398-1.0.1.1-83xiMtxidfyynSF87kmRaKA5haRCNm4h16GK5ZG.cL51tOcjdVT1HOR810.DHFyz9pfc2g9njM9AqF._hgzT1yyY8OAkrny2juH6G4YIYA',
        'Content-Type': 'application/json',
        'Accept': 'application/json'
      }
};

// ✅ Login: Validate mobile number and store OTP in Redis
async function login(mobilenumber) {
  try {
    const response = await axios.get(apiConfig.baseURL, {
      params: { mobile_number: mobilenumber },
      headers: apiConfig.headers,
      timeout: apiConfig.timeout
    });

    console.log(`📡 Freshdesk count:`, response.data.count);

    if (response.data.count === 1) {
      // Generate 4-digit OTP
      const otp = Math.floor(1000 + Math.random() * 9000).toString(); 

      // Store in Redis with 2 minutes expiry
      await redisClient.setEx(`otp:${mobilenumber}`, 120, otp);

      // Simulate OTP sending (replace with SMS API later)
      console.log(`📨 OTP for ${mobilenumber}: ${otp}`);
      return {message:  'OTP Sent Successfully', mobilenumber: mobilenumber, otp: otp};
    } else {
      console.log(`❌ Mobile Number not registered`);
      return {message: 'Mobile Number not registered', mobilenumber: mobilenumber};
    }
  } catch (error) {
    console.error('❌ Error in login():', error.message);
    throw error;
  }
}

// ✅ Verify OTP using Redis
async function verifyOTP(mobilenumber, otp) {
  try {
    const storedOtp = await redisClient.get(`otp:${mobilenumber}`);

    if (!storedOtp) {
      return { success: false, message: 'OTP expired or not found' };
    }

    if (storedOtp !== otp) {
      return { success: false, message: 'Invalid OTP' };
    }

    // ✅ Clean up OTP
    await redisClient.del(`otp:${mobilenumber}`);
    return { success: true, message: 'OTP verified successfully' };
  } catch (error) {
    console.error('❌ Error in verifyOTP():', error.message);
    throw error;
  }
}



module.exports = {
  login,
  verifyOTP
};
