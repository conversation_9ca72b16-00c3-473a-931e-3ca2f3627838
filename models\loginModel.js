const axios = require('axios');
const db = require('../config/db');
// const { createClient } = require('redis'); // Disabled - Redis not running

// 🔌 Connect to Red<PERSON> (disabled for now)
// const redisClient = createClient();
// redisClient.connect().catch(console.error);

// Temporary in-memory storage for OTPs (for deve lopment only)
const otpStorage = new Map();
// console.log(otpStorage);


// 🔐 Freshdesk API config
const apiConfig = {
  baseURL: 'https://godrejcpteam.freshdesk.com/api/v2/custom_objects/schemas/5974899/records',
  timeout: 10000,
  headers: {
        'Authorization': 'Basic MG9jN1UyWWc4bkM4UnMxVzhwRQ==',
        'Cookie': '_cf_bm=xOxh95NoVwozSEhMrsCcmXJX1AR2VdeZ_2V6LeVLV4I-1753763398-1.0.1.1-83xiMtxidfyynSF87kmRaKA5haRCNm4h16GK5ZG.cL51tOcjdVT1HOR810.DHFyz9pfc2g9njM9AqF._hgzT1yyY8OAkrny2juH6G4YIYA',
        'Content-Type': 'application/json',
        'Accept': 'application/json'
      }
};

// ✅ Login: Validate mobile number and store OTP in Redis
async function login(mobilenumber) {
  try {
    const response = await axios.get(apiConfig.baseURL, {
      params: { mobile_number: mobilenumber },
      headers: apiConfig.headers,
      timeout: apiConfig.timeout
    });

    console.log(`📡 Freshdesk count:`, response.data.count);
    console.log(`📡 Freshdesk records:`, response?.data?.records?.length);
    if (response?.data?.records?.length > 0) {
      // Generate 4-digit OTP
      const otp = Math.floor(1000 + Math.random() * 9000).toString(); 

      // Store in memory with 5 minutes expiry (temporary solution)
      otpStorage.set(mobilenumber, {
        otp: otp,
        expires: Date.now() + 5 * 60 * 1000  // 5 minutes from now
      });

      const insert = await db.query(`INSERT INTO otps (mobile, otp, expiry) VALUES (?, ?, ?)`, [mobilenumber, otp, Date.now() + 5 * 60 * 1000]);

      // Simulate OTP sending (replace with SMS API later)
      console.log(`📨 OTP for ${mobilenumber}: ${otp}`);
      if (insert) {
      return {message: 'OTP Sent Successfully', mobilenumber: mobilenumber, otp: otp};
        }
    } else {
      console.log(`❌ Mobile Number not registered`);
      return 'Mobile Number not registered';
    }
  } catch (error) {
    console.error('❌ Error in login():', error.message);
    throw error;
  }
}

// ✅ Verify OTP using in-memory storage
async function verifyOTP(mobilenumber, otp) {
  try {
    // Check if OTP exists in memory
    // console.log(otpStorage);
    // const storedData = otpStorage.get(mobilenumber);


    const storedData = await db.query(`SELECT * FROM otps WHERE mobile = ? ORDER BY id DESC LIMIT 1`, [mobilenumber]);
    console.log(`verifyOTP: ${storedData[0]}`);

    if (!storedData) {
      return { success: false, message: 'OTP expired or not found' };
    }

    // Check if OTP has expired
    if (Date.now() > storedData[0].expiry) {
      // otpStorage.delete(mobilenumber);
      return { success: false, message: 'OTP expired or not found' };
    }

    if (storedData[0].otp !== otp) {
      return { success: false, message: 'Invalid OTP' };
    }

    // ✅ Clean up OTP
    otpStorage.delete(mobilenumber);
    return { success: true, message: 'OTP verified successfully' };
  } catch (error) {
    console.error('❌ Error in verifyOTP():', error.message);
    throw error;
  }
}



module.exports = {
  login,
  verifyOTP
};
