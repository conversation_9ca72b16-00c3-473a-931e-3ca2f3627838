const listModel = require('../models/listModel');

// list
async function list(req, res) {
    try {
        const list = await listModel.list();
        res.status(200).json({
            status: 200,
            message: 'List retrieved successfully',
            data: list,
        });
    } catch (error) {
        res.status(500).json({ 
                status: 500,
                message: 'Internal server error',
                error: error.message
            });
    }
}


// Export the list function
async function tickets(req, res) { 
    try {
        const tickets = await listModel.tickets();    
        res.status(200).json({
            status: 200,
            message: 'Tickets retrieved successfully',
            data: tickets,
        });
    } catch (error) {
        res.status(500).json({ 
                status: 500,
                message: 'Internal server error',
                error: error.message
            });
    }
}

async function custom_objects(req, res) { 
    try {
        // console.log('req.body', req.body);
        const data = req.body;
        const custom_objects = await listModel.custom_objects(data);
        res.status(200).json({
            status: 200,
            message: 'Custom objects retrieved successfully',
            data: custom_objects,
        });
    } catch (error) {    
        if (error.response) {
        res.status(error.response.status).json({ 
                status: error.response.status,
                message: error.response.data.message,
                error: error.response.data.errors,
                data: error.response.data.data
            });
    }
}
}

// submit invoice 
async function submit_invoice(req, res) { 
    try {
        const data = req.body;
        const submit_invoice = await listModel.submit_invoice(data);
        res.status(200).json({
            status: 200,
            message: 'Invoice submitted successfully',
            data: submit_invoice,
        });
    }
    catch (error) {    
        if (error.response) {
        res.status(500).json({ 
                status: error.response.status,
                message: error.response.data.message,
                error: error.response.data.errors,
                data: error.response.data.data
            });
    }
}
}

// no update 
async function no_update(req, res) { 
    try {
        const data = req.body;
        const no_update = await listModel.no_update(data);
        res.status(200).json({
            status: 200,
            message: 'No update successfully',
            data: no_update,
        });
    }
    catch (error) {    
        if (error.response) {
        res.status(500).json({ 
                status: error.response.status,
                message: error.response.data.message,
                error: error.response.data.errors,
                data: error.response.data.data
            });
    }
}
}


module.exports = {
    list,
    tickets,
    custom_objects,
    submit_invoice,
    no_update
};
