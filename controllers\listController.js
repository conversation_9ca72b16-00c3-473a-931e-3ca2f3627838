const listModel = require('../models/listModel');

// list
async function list(req, res) {
    try {
        const list = await listModel.list();
        res.status(200).json({
            status: 200,
            message: 'List retrieved successfully',
            data: list,
        });
    } catch (error) {
        res.status(500).json({ 
                status: 500,
                message: 'Internal server error',
                error: error.message
            });
    }
}


// Export the list function
async function userlist(req, res) { 
    try {
        const userlist = await listModel.userlist();    
        res.status(200).json({
            status: 200,
            message: 'Userlist retrieved successfully',
            data: userlist,
        });
    } catch (error) {
        res.status(500).json({ 
                status: 500,
                message: 'Internal server error',
                error: error.message
            });
    }
}

module.exports = {
    list,
    userlist
};
