const express = require('express');
const listController = require('../controllers/listController');
const router = express.Router();


// List route
router.get('/', listController.list);
// User list route  
router.get('/tickets', listController.tickets);

// Custom objects route
router.post('/custom_objects', listController.custom_objects);

// Export the router
router.put('/submit_invoice', listController.submit_invoice);

// no update route
router.put('/no_update', listController.no_update);

module.exports = router;
